// ==UserScript==
// @name         Script Teste
// @namespace    script-test
// @version      1.0.0
// <AUTHOR>
// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// @license MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

// ===== CONFIGURAÇÕES =====
const CONFIG = {
	SCRIPT_NAME: "teste",
	VERSION: "1.0.30",
	STORAGE_KEYS: {
		SETTINGS: "settings",
	},
} as const;

// ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
class StorageService {
	static get<T>(key: string): T | null {
		try {
			const item = localStorage.getItem(key);
			return item ? JSON.parse(item) : null;
		} catch {
			return null;
		}
	}
	static setJSON<T>(key: string, value: T): boolean {
		try {
			localStorage.setItem(key, JSON.stringify(value));
			return true;
		} catch {
			return false;
		}
	}
	static remove(key: string): boolean {
		try {
			localStorage.removeItem(key);
			return true;
		} catch {
			return false;
		}
	}
}

// ===== UTILITÁRIOS DOM ESSENCIAL =====
interface ElementOptions {
	className?: string;
	textContent?: string;
	styles?: Partial<CSSStyleDeclaration>;
	attributes?: Record<string, string>;
	eventListeners?: Record<string, EventListener>;
}
class DOMUtilities {
	static createElement<T extends HTMLElement>(tag: string, options: ElementOptions = {}): T {
		const element = document.createElement(tag) as T;
		if (options.className) element.className = options.className;
		if (options.textContent) element.textContent = options.textContent;
		if (options.styles) Object.assign(element.style, options.styles);
		if (options.attributes)
			Object.entries(options.attributes).forEach(([key, value]) => {
				element.setAttribute(key, value);
			});
		if (options.eventListeners)
			Object.entries(options.eventListeners).forEach(([event, listener]) => {
				element.addEventListener(event, listener);
			});
		return element;
	}
	static removeElement(element: HTMLElement | null): boolean {
		if (element && element.parentNode) {
			element.parentNode.removeChild(element);
			return true;
		}
		return false;
	}
}

// ===== GERENCIAMENTO DE ESTADO ESSENCIAL =====
type SettingsValue = string | number | boolean | object | null;
class SettingsStore {
	private static instance: SettingsStore;
	private settings: Record<string, SettingsValue> = {};
	private readonly storageKey = CONFIG.STORAGE_KEYS.SETTINGS;

	private constructor() {
		this.loadSettings();
	}

	static getInstance(): SettingsStore {
		if (!SettingsStore.instance) {
			SettingsStore.instance = new SettingsStore();
		}
		return SettingsStore.instance;
	}

	private loadSettings(): void {
		const savedSettings = StorageService.get<Record<string, SettingsValue>>(this.storageKey);
		if (savedSettings && typeof savedSettings === "object") {
			this.settings = savedSettings;
		}
	}

	private saveSettings(): boolean {
		return StorageService.setJSON(this.storageKey, this.settings);
	}

	getAllSettings(): Record<string, SettingsValue> {
		return { ...this.settings };
	}

	getSetting<T extends SettingsValue>(key: string): T | null {
		return (this.settings[key] as T) || null;
	}

	setSetting(key: string, value: SettingsValue): boolean {
		this.settings[key] = value;
		return this.saveSettings();
	}
}

// ===== GERENCIADOR DE KEYBINDS ESSENCIAL =====
type KeyHandler = (event: KeyboardEvent) => void | boolean;
interface KeyBinding {
	key: string;
	ctrl?: boolean;
	shift?: boolean;
	alt?: boolean;
	handler: KeyHandler;
	description?: string;
}
class KeyBindManager {
	private static instance: KeyBindManager;
	private keyBindings: Map<string, KeyBinding> = new Map();

	private constructor() {
		this.setupGlobalListener();
	}

	static getInstance(): KeyBindManager {
		if (!KeyBindManager.instance) {
			KeyBindManager.instance = new KeyBindManager();
		}
		return KeyBindManager.instance;
	}

	private setupGlobalListener(): void {
		document.addEventListener("keydown", event => {
			const key = event.key.toLowerCase();
			const binding = this.keyBindings.get(key);
			if (binding) {
				const result = binding.handler(event);
				if (result !== false) {
					event.preventDefault();
					event.stopPropagation();
				}
			}
		});
	}

	register(binding: KeyBinding): boolean {
		const key = binding.key.toLowerCase();
		this.keyBindings.set(key, binding);
		return true;
	}

	listBindings(): KeyBinding[] {
		return Array.from(this.keyBindings.values());
	}
}

// ===== DECODIFICADOR DE PROTOCOLO AGAR.IO =====
class AgarProtocolDecoder {
	// Códigos de pacotes conhecidos do Agar.io
	private static readonly PACKET_TYPES: Record<number, string> = {
		16: "UPDATE_NODES", // Atualização das células
		17: "UPDATE_POSITION", // Posição da própria célula
		18: "CLEAR_ALL", // Limpar todas as células
		20: "DRAW_LINE", // Linha entre células
		21: "DEBUG_LINE", // Debug
		32: "OWN_ID", // ID da própria célula
		49: "FFA_LEADERBOARD", // Leaderboard FFA
		50: "TEAMS_LEADERBOARD", // Leaderboard Teams
		64: "BORDER", // Bordas do mapa
		81: "DRAW_LINE", // Linhas visuais
	};

	static decodeMessage(data: any): any {
		try {
			// Se for string, tenta converter para ArrayBuffer
			if (typeof data === "string") {
				const buffer = new ArrayBuffer(data.length);
				const view = new Uint8Array(buffer);
				for (let i = 0; i < data.length; i++) {
					view[i] = data.charCodeAt(i);
				}
				data = buffer;
			}

			// Se for ArrayBuffer, converte para DataView
			if (data instanceof ArrayBuffer) {
				const view = new DataView(data);
				const packetId = view.getUint8(0);

				const decoded = {
					packetId,
					packetType: this.PACKET_TYPES[packetId] || "UNKNOWN",
					rawSize: data.byteLength,
					data: this.decodePacketData(packetId, view),
					hexDump: this.createHexDump(data),
				};

				return decoded;
			}

			// Se for Blob, converte para ArrayBuffer
			if (data instanceof Blob) {
				return new Promise(resolve => {
					const reader = new FileReader();
					reader.onload = () => {
						resolve(this.decodeMessage(reader.result));
					};
					reader.readAsArrayBuffer(data);
				});
			}

			return { type: "unknown", originalData: data };
		} catch (error) {
			return { type: "error", error: error instanceof Error ? error.message : "Unknown error", originalData: data };
		}
	}

	private static decodePacketData(packetId: number, view: DataView): any {
		try {
			switch (packetId) {
				case 16: // UPDATE_NODES
					return this.decodeUpdateNodes(view);
				case 17: // UPDATE_POSITION
					return this.decodeUpdatePosition(view);
				case 32: // OWN_ID
					return this.decodeOwnId(view);
				case 49: // FFA_LEADERBOARD
					return this.decodeLeaderboard(view);
				case 64: // BORDER
					return this.decodeBorder(view);
				default:
					return this.decodeGenericData(view);
			}
		} catch (error) {
			return { error: "Failed to decode packet data", details: error instanceof Error ? error.message : "Unknown error" };
		}
	}

	private static decodeUpdateNodes(view: DataView): any {
		const result: any = { cells: [], destroyedCells: [] };
		let offset = 1; // Pular o packet ID

		try {
			// Ler número de células para deletar
			const destroyCount = view.getUint16(offset, true);
			offset += 2;

			// Ler IDs das células para deletar
			for (let i = 0; i < destroyCount; i++) {
				const cellId = view.getUint32(offset, true);
				result.destroyedCells.push(cellId);
				offset += 4;
			}

			// Ler células atualizadas
			while (offset < view.byteLength) {
				if (offset + 4 >= view.byteLength) break;

				const cellId = view.getUint32(offset, true);
				offset += 4;

				if (cellId === 0) break; // Fim das células

				const cell: any = { id: cellId };

				// Posição X
				cell.x = view.getInt32(offset, true);
				offset += 4;

				// Posição Y
				cell.y = view.getInt32(offset, true);
				offset += 4;

				// Tamanho
				cell.size = view.getUint16(offset, true);
				offset += 2;

				// Flags
				const flags = view.getUint8(offset);
				offset += 1;

				cell.flags = {
					isVirus: (flags & 1) !== 0,
					isAgitated: (flags & 16) !== 0,
					isEjectedMass: (flags & 4) !== 0,
					isFood: (flags & 2) !== 0,
				};

				// Cor (se tiver)
				if (flags & 2) {
					cell.color = {
						r: view.getUint8(offset),
						g: view.getUint8(offset + 1),
						b: view.getUint8(offset + 2),
					};
					offset += 3;
				}

				// Skin (se tiver)
				if (flags & 4) {
					let skinName = "";
					while (offset < view.byteLength) {
						const char = view.getUint16(offset, true);
						offset += 2;
						if (char === 0) break;
						skinName += String.fromCharCode(char);
					}
					cell.skin = skinName;
				}

				// Nome (se tiver)
				if (flags & 8) {
					let name = "";
					while (offset < view.byteLength) {
						const char = view.getUint16(offset, true);
						offset += 2;
						if (char === 0) break;
						name += String.fromCharCode(char);
					}
					cell.name = name;
				}

				result.cells.push(cell);
			}
		} catch (error) {
			result.parseError = error instanceof Error ? error.message : "Unknown error";
		}

		return result;
	}

	private static decodeUpdatePosition(view: DataView): any {
		return {
			x: view.getFloat32(1, true),
			y: view.getFloat32(5, true),
			zoom: view.getFloat32(9, true),
		};
	}

	private static decodeOwnId(view: DataView): any {
		const ids = [];
		let offset = 1;

		while (offset + 4 <= view.byteLength) {
			ids.push(view.getUint32(offset, true));
			offset += 4;
		}

		return { ownIds: ids };
	}

	private static decodeLeaderboard(view: DataView): any {
		const leaderboard = [];
		let offset = 1;

		try {
			const count = view.getUint32(offset, true);
			offset += 4;

			for (let i = 0; i < count; i++) {
				const isMe = view.getUint32(offset, true) === 1;
				offset += 4;

				let name = "";
				while (offset < view.byteLength) {
					const char = view.getUint16(offset, true);
					offset += 2;
					if (char === 0) break;
					name += String.fromCharCode(char);
				}

				leaderboard.push({ position: i + 1, name, isMe });
			}
		} catch (error) {
			return { error: error instanceof Error ? error.message : "Unknown error" };
		}

		return { leaderboard };
	}

	private static decodeBorder(view: DataView): any {
		return {
			minX: view.getFloat64(1, true),
			minY: view.getFloat64(9, true),
			maxX: view.getFloat64(17, true),
			maxY: view.getFloat64(25, true),
		};
	}

	private static decodeGenericData(view: DataView): any {
		const data = [];
		for (let i = 0; i < Math.min(view.byteLength, 50); i++) {
			data.push(view.getUint8(i));
		}
		return { genericBytes: data };
	}

	private static createHexDump(buffer: ArrayBuffer): string {
		const bytes = new Uint8Array(buffer);
		const lines = [];

		for (let i = 0; i < Math.min(bytes.length, 128); i += 16) {
			const hex = Array.from(bytes.slice(i, i + 16))
				.map(b => b.toString(16).padStart(2, "0"))
				.join(" ");
			const ascii = Array.from(bytes.slice(i, i + 16))
				.map(b => (b >= 32 && b <= 126 ? String.fromCharCode(b) : "."))
				.join("");
			lines.push(`${i.toString(16).padStart(8, "0")}: ${hex.padEnd(47)} |${ascii}|`);
		}

		if (bytes.length > 128) {
			lines.push(`... (${bytes.length - 128} bytes omitted)`);
		}

		return lines.join("\n");
	}
}

// ===== INTERCEPTADOR DE WEBSOCKET =====
class WebSocketInterceptor {
	private static instance: WebSocketInterceptor;
	private interceptedSocket: WebSocket | null = null;
	private logInterval: number | null = null;
	private originalWebSocket: typeof WebSocket;
	private messageBuffer: Array<{ timestamp: number; data: any; decoded?: any; type: "sent" | "received" }> = [];

	// Singleton access
	static getInstance(): WebSocketInterceptor {
		if (!WebSocketInterceptor.instance) {
			WebSocketInterceptor.instance = new WebSocketInterceptor();
		}
		return WebSocketInterceptor.instance;
	}

	private constructor() {
		this.originalWebSocket = window.WebSocket;
		this.interceptWebSocket();
	}

	// Intercepta conexões WebSocket para o servidor alvo
	private interceptWebSocket(): void {
		const self = this;
		window.WebSocket = class extends self.originalWebSocket {
			constructor(url: string | URL, protocols?: string | string[]) {
				super(url, protocols);
				const urlString = url.toString();
				if (urlString.includes("servers.agariobr.com.br:4409")) {
					self.interceptedSocket = this;
					self.startLogging();
					self.attachSocketListeners(this);
				}
			}
		};
		Object.setPrototypeOf(window.WebSocket.prototype, this.originalWebSocket.prototype);
		Object.setPrototypeOf(window.WebSocket, this.originalWebSocket);
	}

	// Adiciona listeners para capturar mensagens e eventos do socket
	private attachSocketListeners(socket: WebSocket): void {
		const originalSend = socket.send.bind(socket);
		socket.send = (data: string | ArrayBufferLike | Blob | ArrayBufferView) => {
			const decoded = AgarProtocolDecoder.decodeMessage(data);
			this.messageBuffer.push({ timestamp: Date.now(), data, decoded, type: "sent" });
			return originalSend(data);
		};

		socket.addEventListener("message", event => {
			const decoded = AgarProtocolDecoder.decodeMessage(event.data);
			this.messageBuffer.push({ timestamp: Date.now(), data: event.data, decoded, type: "received" });
		});

		socket.addEventListener("open", () => {
			console.log("🟢 WebSocket conectado ao Agar.io!");
		});

		socket.addEventListener("close", () => {
			console.log("🔴 WebSocket desconectado do Agar.io!");
			this.stopLogging();
			this.interceptedSocket = null;
		});

		socket.addEventListener("error", () => {
			console.log("❌ Erro no WebSocket do Agar.io!");
		});
	}

	// Inicia logging periódico do status e mensagens
	private startLogging(): void {
		if (this.logInterval) clearInterval(this.logInterval);
		this.logInterval = setInterval(() => {
			if (this.interceptedSocket && this.interceptedSocket.readyState === WebSocket.OPEN) {
				this.logSocketStatus();
				this.logRecentMessages();
			}
		}, 3000) as unknown as number;
	}

	private stopLogging(): void {
		if (this.logInterval) {
			clearInterval(this.logInterval);
			this.logInterval = null;
		}
	}

	// Loga status do socket
	private logSocketStatus(): void {
		if (!this.interceptedSocket) return;
		const states: Record<number, string> = {
			[WebSocket.CONNECTING]: "CONNECTING",
			[WebSocket.OPEN]: "OPEN",
			[WebSocket.CLOSING]: "CLOSING",
			[WebSocket.CLOSED]: "CLOSED",
		};
		console.group("📡 Status WebSocket");
		console.log(`Estado: ${states[this.interceptedSocket.readyState]}`);
		console.log(`URL: ${this.interceptedSocket.url}`);
		console.log(`Protocolo: ${this.interceptedSocket.protocol || "N/A"}`);
		console.log(`Extensões: ${this.interceptedSocket.extensions || "N/A"}`);
		console.log(`Mensagens no buffer: ${this.messageBuffer.length}`);
		console.groupEnd();
	}

	// Loga mensagens recentes
	private logRecentMessages(): void {
		const now = Date.now();
		const recentMessages = this.messageBuffer.filter(msg => now - msg.timestamp < 3000);
		if (recentMessages.length > 0) {
			console.group("📨 Mensagens dos últimos 3 segundos");
			recentMessages.forEach(msg => {
				const timeStr = new Date(msg.timestamp).toLocaleTimeString();
				const icon = msg.type === "sent" ? "📤" : "📥";

				// Mostra apenas informação decodificada
				if (msg.decoded && msg.decoded.packetType !== "UNKNOWN") {
					console.log(`${icon} [${timeStr}] ${msg.decoded.packetType}:`, msg.decoded.data);
				}
			});
			console.groupEnd();
		}
		if (this.messageBuffer.length > 100) {
			this.messageBuffer = this.messageBuffer.slice(-100);
		}
	}

	// Retorna informações do WebSocket interceptado
	getSocketInfo(): object | null {
		if (!this.interceptedSocket) return null;
		return {
			url: this.interceptedSocket.url,
			readyState: this.interceptedSocket.readyState,
			protocol: this.interceptedSocket.protocol,
			extensions: this.interceptedSocket.extensions,
			messagesCount: this.messageBuffer.length,
		};
	}

	// Retorna mensagens recentes (em segundos)
	getRecentMessages(seconds: number = 10): Array<{ timestamp: number; data: any; decoded?: any; type: "sent" | "received" }> {
		const cutoff = Date.now() - seconds * 1000;
		return this.messageBuffer.filter(msg => msg.timestamp > cutoff);
	}
}

// ===== GERENCIADOR DE UI ESSENCIAL =====
interface UIComponent {
	render(): HTMLElement;
	destroy(): void;
}
class UIManager {
	private static instance: UIManager;
	private readonly keyBindManager: KeyBindManager;
	private isInitialized = false;

	private constructor() {
		this.keyBindManager = KeyBindManager.getInstance();
	}

	static getInstance(): UIManager {
		if (!UIManager.instance) UIManager.instance = new UIManager();
		return UIManager.instance;
	}

	initialize(): boolean {
		if (this.isInitialized) return true;

		this.keyBindManager.register({
			key: "F1",
			handler: () => alert("Ajuda"),
			description: "Mostra a ajuda",
		});

		// Keybind para mostrar status do WebSocket
		this.keyBindManager.register({
			key: "F2",
			handler: () => {
				const interceptor = WebSocketInterceptor.getInstance();
				const info = interceptor.getSocketInfo();
				console.log("🔍 Informações do WebSocket:", info);
				const recent = interceptor.getRecentMessages(30);
				const decodedOnly = recent.filter(msg => msg.decoded && msg.decoded.packetType !== "UNKNOWN");
				console.log(
					"🕒 Mensagens decodificadas dos últimos 30 segundos:",
					decodedOnly.map(msg => ({
						time: new Date(msg.timestamp).toLocaleTimeString(),
						type: msg.type,
						packet: msg.decoded.packetType,
						data: msg.decoded.data,
					}))
				);
			},
			description: "Mostra informações do WebSocket",
		});

		// Keybind para análise detalhada de pacotes
		this.keyBindManager.register({
			key: "F3",
			handler: () => {
				const interceptor = WebSocketInterceptor.getInstance();
				const recent = interceptor.getRecentMessages(60);

				console.group("🔬 Análise Detalhada dos Pacotes");

				// Contar tipos de pacotes
				const packetStats: Record<string, number> = {};
				recent.forEach(msg => {
					if (msg.decoded && msg.decoded.packetType) {
						const key = `${msg.type.toUpperCase()}_${msg.decoded.packetType}`;
						packetStats[key] = (packetStats[key] || 0) + 1;
					}
				});

				console.log("📊 Estatísticas de pacotes (último minuto):", packetStats);

				// Mostrar pacotes de células
				const cellUpdates = recent.filter(msg => msg.decoded && msg.decoded.packetType === "UPDATE_NODES");

				if (cellUpdates.length > 0) {
					console.group("🔴 Atualizações de Células");
					cellUpdates.slice(-5).forEach((msg, idx) => {
						console.log(`Update ${idx + 1}:`, msg.decoded.data);
					});
					console.groupEnd();
				}

				// Mostrar leaderboard
				const leaderboards = recent.filter(msg => msg.decoded && msg.decoded.packetType === "FFA_LEADERBOARD");

				if (leaderboards.length > 0) {
					console.group("🏆 Leaderboards");
					leaderboards.slice(-1).forEach(msg => {
						console.log("Último leaderboard:", msg.decoded.data);
					});
					console.groupEnd();
				}

				console.groupEnd();
			},
			description: "Análise detalhada dos pacotes",
		});

		// Keybind para filtrar por tipo de pacote
		this.keyBindManager.register({
			key: "F4",
			handler: () => {
				const type = prompt("Digite o tipo de pacote para filtrar (ex: UPDATE_NODES, FFA_LEADERBOARD, OWN_ID):");
				if (!type) return;

				const interceptor = WebSocketInterceptor.getInstance();
				const recent = interceptor.getRecentMessages(120);
				const filtered = recent.filter(msg => msg.decoded && msg.decoded.packetType === type.toUpperCase());

				console.group(`🎯 Pacotes do tipo: ${type.toUpperCase()}`);
				console.log(`Encontrados ${filtered.length} pacotes nos últimos 2 minutos`);

				filtered.slice(-10).forEach((msg, idx) => {
					const timeStr = new Date(msg.timestamp).toLocaleTimeString();
					console.log(`${idx + 1}. [${timeStr}] ${msg.type}:`, msg.decoded);
				});

				console.groupEnd();
			},
			description: "Filtrar mensagens por tipo de pacote",
		});

		this.isInitialized = true;
		return true;
	}

	destroy(): void {
		this.isInitialized = false;
	}
}

// ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
class ScriptApplication {
	private static instance: ScriptApplication;
	private readonly settingsStore: SettingsStore;
	private readonly uiManager: UIManager;
	private readonly wsInterceptor: WebSocketInterceptor;
	private isInitialized = false;

	private constructor() {
		this.settingsStore = SettingsStore.getInstance();
		this.uiManager = UIManager.getInstance();
		this.wsInterceptor = WebSocketInterceptor.getInstance();
	}

	static getInstance(): ScriptApplication {
		if (!ScriptApplication.instance) ScriptApplication.instance = new ScriptApplication();
		return ScriptApplication.instance;
	}

	async initialize(): Promise<boolean> {
		if (this.isInitialized) return true;

		if (document.readyState === "loading") {
			await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
		}

		this.uiManager.initialize();
		this.settingsStore.setSetting("appVersion", CONFIG.VERSION);

		console.log("🚀 Script inicializado com interceptação WebSocket");
		console.log("ℹ️  Controles disponíveis:");
		console.log("   F1 - Ajuda");
		console.log("   F2 - Status do WebSocket");
		console.log("   F3 - Análise detalhada dos pacotes");
		console.log("   F4 - Filtrar por tipo de pacote");

		this.isInitialized = true;
		return true;
	}
}

// ===== INICIALIZAÇÃO AUTOMÁTICA =====
ScriptApplication.getInstance().initialize();
